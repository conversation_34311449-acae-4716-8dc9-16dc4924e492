import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Search, Loader2, Calendar, Star, Play, Film } from "lucide-react";
import { searchContent, buildImageUrl, TMDBMovie, TMDBTVShow, getContentDetails, isValidTMDBId } from "@/services/tmdbService";

interface TMDBSearchDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectContent: (tmdbId: string, contentType: 'movie' | 'tv') => void;
}

export default function TMDBSearchDialog({ isOpen, onClose, onSelectContent }: TMDBSearchDialogProps) {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<(TMDBMovie | TMDBTVShow)[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchType, setSearchType] = useState<'movie' | 'tv' | 'all'>('all');

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: "Error",
        description: "Please enter a search query",
        variant: "destructive",
      });
      return;
    }

    setIsSearching(true);

    try {
      // Check if the search query is a valid TMDB ID
      if (isValidTMDBId(searchQuery.trim())) {
        // Direct lookup by TMDB ID
        const tmdbId = searchQuery.trim();

        // Try to get content details directly
        try {
          let contentData;

          if (searchType === 'movie') {
            // Direct movie lookup
            contentData = await getContentDetails(tmdbId, 'movie');
          } else if (searchType === 'tv') {
            // Direct TV show lookup
            contentData = await getContentDetails(tmdbId, 'tv');
          } else {
            // Try both movie and TV show (auto-detect)
            contentData = await getContentDetails(tmdbId);
          }

          // Convert to search results format
          setSearchResults([contentData]);

          const contentType = 'title' in contentData ? 'Movie' : 'TV Show';
          toast({
            title: "Content Found",
            description: `Found ${contentType} by TMDB ID: ${tmdbId}`,
          });

        } catch (error) {
          // If direct lookup fails, fall back to search
          console.warn('Direct TMDB ID lookup failed, falling back to search:', error);

          const results = await searchContent(
            searchQuery,
            searchType === 'all' ? undefined : searchType
          );

          setSearchResults(results.results);

          toast({
            title: "Search Complete",
            description: `Found ${results.total_results} results`,
          });
        }
      } else {
        // Regular text search
        const results = await searchContent(
          searchQuery,
          searchType === 'all' ? undefined : searchType
        );

        setSearchResults(results.results);

        toast({
          title: "Search Complete",
          description: `Found ${results.total_results} results`,
        });
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Search failed";

      toast({
        title: "Search Error",
        description: errorMessage,
        variant: "destructive",
      });

      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectContent = (item: TMDBMovie | TMDBTVShow) => {
    const contentType = 'title' in item ? 'movie' : 'tv';
    onSelectContent(item.id.toString(), contentType);
    onClose();
    
    toast({
      title: "Content Selected",
      description: `Selected ${contentType}: ${item.title || item.name}`,
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Search TMDB Database</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Search Controls */}
          <div className="space-y-4">
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by title or enter TMDB ID (e.g., 550 for Fight Club)..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="pl-10"
                />
              </div>
              <Button 
                onClick={handleSearch} 
                disabled={isSearching || !searchQuery.trim()}
              >
                {isSearching ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant={searchType === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSearchType('all')}
              >
                All
              </Button>
              <Button
                variant={searchType === 'movie' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSearchType('movie')}
              >
                Movies
              </Button>
              <Button
                variant={searchType === 'tv' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSearchType('tv')}
              >
                TV Shows
              </Button>
            </div>
          </div>

          {/* Search Results */}
          <div className="space-y-4">
            {isSearching && (
              <div className="text-center py-8">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                <p className="text-muted-foreground">Searching TMDB...</p>
              </div>
            )}
            
            {!isSearching && searchResults.length === 0 && searchQuery && (
              <div className="text-center py-8 text-muted-foreground">
                No results found for "{searchQuery}"
              </div>
            )}
            
            {!isSearching && searchResults.length > 0 && (
              <div className="grid gap-4">
                {searchResults.slice(0, 20).map((item) => {
                  const isMovie = 'title' in item;
                  const title = isMovie ? item.title : item.name;
                  const releaseDate = isMovie ? item.release_date : item.first_air_date;
                  const year = releaseDate ? new Date(releaseDate).getFullYear() : null;
                  const posterUrl = buildImageUrl(item.poster_path, 'poster', 'small');
                  
                  return (
                    <div
                      key={item.id}
                      className="flex gap-4 p-4 border border-border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                      onClick={() => handleSelectContent(item)}
                    >
                      {/* Poster */}
                      <div className="flex-shrink-0">
                        {posterUrl ? (
                          <img
                            src={posterUrl}
                            alt={title}
                            className="w-16 h-24 object-cover rounded"
                          />
                        ) : (
                          <div className="w-16 h-24 bg-muted rounded flex items-center justify-center">
                            {isMovie ? (
                              <Film className="h-6 w-6 text-muted-foreground" />
                            ) : (
                              <Play className="h-6 w-6 text-muted-foreground" />
                            )}
                          </div>
                        )}
                      </div>
                      
                      {/* Content Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1">
                            <h3 className="font-semibold text-sm line-clamp-1">
                              {title}
                            </h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant={isMovie ? 'default' : 'secondary'} className="text-xs">
                                {isMovie ? 'Movie' : 'TV Show'}
                              </Badge>
                              {year && (
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <Calendar className="h-3 w-3" />
                                  {year}
                                </div>
                              )}
                              {item.vote_average > 0 && (
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <Star className="h-3 w-3" />
                                  {item.vote_average.toFixed(1)}
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ID: {item.id}
                          </div>
                        </div>
                        
                        {item.overview && (
                          <p className="text-xs text-muted-foreground mt-2 line-clamp-2">
                            {item.overview}
                          </p>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
