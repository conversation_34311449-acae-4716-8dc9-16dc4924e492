import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { But<PERSON> } from "@/components/ui/button";
import { Toggle } from "@/components/ui/toggle";
import { ChevronDown, FileUp, Star, Video, UploadCloud, Info, CheckCircle, X, Upload, Loader2, Search, Plus, Trash2 } from "lucide-react";
import safeStorage from "@/utils/safeStorage";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { encodeVideoLinks, parseVideoLinks, isValidVideoLink } from "@/utils/videoSecurity";
import SecureVideoPlayer from "@/components/SecureVideoPlayer";
import {
  getComprehensiveContentData,
  getPosterUrls,
  getBackdropUrls,
  isValidTMDBId,
  TMDBError
} from "@/services/tmdbService";
import TMDBSearchDialog from "./TMDBSearchDialog";
import OMDBSearchDialog from "./OMDBSearchDialog";
import BulkAddMode from "./BulkAddMode";
import { MediaItem } from "@/types/media";
import apiService from "@/services/apiService";
import { ContentSection } from "@/types/sections";

const GENRES = [
  "Action", "Adventure", "Comedy", "Drama", "Fantasy", "Thriller", "Horror", "Sci-Fi", "Romance", "Crime", "Mystery", "Animation", "Family"
];
const LANGUAGES = [
  "Hindi", "English", "Tamil", "Telugu", "Malayalam", "Korean", "Japanese", "Anime"
];
const QUALITY = ["HD", "WEB", "BluRay", "Cam", "HDTS", "HDTC"];

const CATEGORIES = [
  "Hindi Movies",
  "Hindi Web Series",
  "English Movies",
  "English Web Series",
  "Telugu Movies",
  "Telugu Web Series",
  "Tamil Movies",
  "Tamil Web Series",
  "Malayalam Movies",
  "Malayalam Web Series",
  "Korean Movies",
  "Korean Web Series",
  "Japanese Movies",
  "Japanese Web Series",
  "Anime",
  "Hindi Dubbed",
  "English Dubbed",
  "Animation"
];

interface SeasonData {
  seasonNumber: number;
  title: string;
  description: string;
  posterUrl: string;
  episodes: EpisodeData[];
}

interface EpisodeData {
  episodeNumber: number;
  title: string;
  description: string;
  videoLinks: string;
  secureVideoLinks: string;
  runtime: string;
  airDate: string;
  thumbnailUrl: string;
}

interface FormData {
  title: string;
  type: string;
  category: string;
  section: string; // Keep for backward compatibility
  section_ids: number[]; // New field for multiple sections
  tmdbId: string;
  year: string;
  genres: string[];
  languages: string[];
  description: string;
  posterUrl: string;
  thumbnailUrl: string;
  videoLinks: string;
  secureVideoLinks: string; // Encoded video links for security
  quality: string[];
  tags: string;
  imdbRating: string;
  runtime: string;
  studio: string;
  audioTracks: string[];
  trailer: string;
  subtitleFile: File | null;
  subtitleUrl: string;
  isPublished: boolean;
  isFeatured: boolean;
  addToCarousel: boolean;
  // Web Series specific fields
  seasons: SeasonData[];
  totalSeasons: number;
  totalEpisodes: number;
}



export default function AddTitleForm() {
  const { toast } = useToast();
  const [showPosterUrl, setShowPosterUrl] = useState(false);
  const [showThumbnailUrl, setShowThumbnailUrl] = useState(false);
  const [sections, setSections] = useState<ContentSection[]>([]);

  const [showBulkAddMode, setShowBulkAddMode] = useState(false);
  
  const [formData, setFormData] = useState<FormData>({
    title: "",
    type: "movie",
    category: "",
    section: "",
    section_ids: [],
    tmdbId: "",
    year: "",
    genres: [],
    languages: [],
    description: "",
    posterUrl: "",
    thumbnailUrl: "",
    videoLinks: "",
    secureVideoLinks: "",
    quality: [],
    tags: "",
    imdbRating: "",
    runtime: "",
    studio: "",
    audioTracks: [],
    trailer: "",
    subtitleFile: null,
    subtitleUrl: "",
    isPublished: false,
    isFeatured: false,
    addToCarousel: false,
    // Web Series specific fields
    seasons: [],
    totalSeasons: 0,
    totalEpisodes: 0,
  });

  // Load sections on component mount
  useEffect(() => {
    const loadSections = async () => {
      try {
        const result = await apiService.getSections({ active_only: true });
        if (result.success) {
          setSections(result.data);
        }
      } catch (error) {
        console.error('Failed to load sections:', error);
      }
    };

    loadSections();
  }, []);



  const [newGenre, setNewGenre] = useState("");
  const [isFetching, setIsFetching] = useState(false);
  const [tmdbData, setTmdbData] = useState<any>(null);
  const [fetchError, setFetchError] = useState<string>("");
  const [showTMDBSearch, setShowTMDBSearch] = useState(false);
  const [showOMDBSearch, setShowOMDBSearch] = useState(false);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleMultiSelect = (field: keyof FormData, value: string) => {
    const currentArray = formData[field] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    handleInputChange(field, newArray);
  };

  const handleQualityToggle = (quality: string) => {
    handleMultiSelect('quality', quality);
  };

  const handleFetchFromTMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first",
        variant: "destructive",
      });
      return;
    }

    if (!isValidTMDBId(formData.tmdbId)) {
      toast({
        title: "Error",
        description: "Please enter a valid TMDB ID (numbers only)",
        variant: "destructive",
      });
      return;
    }

    setIsFetching(true);
    setFetchError("");

    try {
      toast({
        title: "Fetching from TMDB",
        description: "Retrieving content data from TMDB API...",
      });

      const result = await getComprehensiveContentData(formData.tmdbId);

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch data from TMDB");
      }

      const data = result.data;
      setTmdbData(result.rawData);

      // Update form with fetched data
      setFormData(prev => ({
        ...prev,
        title: data.title,
        type: result.contentType,
        year: data.year,
        description: data.description,
        genres: data.genres,
        languages: data.languages,
        posterUrl: data.posterUrl,
        thumbnailUrl: data.thumbnailUrl,
        imdbRating: data.imdbRating,
        runtime: data.runtime,
        studio: data.studio,
        trailer: data.trailer,
      }));

      toast({
        title: "Success",
        description: `${result.contentType === 'movie' ? 'Movie' : 'TV Series'} data fetched successfully from TMDB!`,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setFetchError(errorMessage);

      toast({
        title: "Error",
        description: `Failed to fetch from TMDB: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleFetchPosterFromTMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first",
        variant: "destructive",
      });
      return;
    }

    if (!isValidTMDBId(formData.tmdbId)) {
      toast({
        title: "Error",
        description: "Please enter a valid TMDB ID (numbers only)",
        variant: "destructive",
      });
      return;
    }

    setIsFetching(true);

    try {
      toast({
        title: "Fetching Poster",
        description: "Retrieving poster from TMDB API...",
      });

      const result = await getComprehensiveContentData(formData.tmdbId);

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch poster from TMDB");
      }

      if (!result.data.posterUrl) {
        throw new Error("No poster available for this content");
      }

      setFormData(prev => ({
        ...prev,
        posterUrl: result.data.posterUrl,
        thumbnailUrl: result.data.thumbnailUrl,
      }));

      toast({
        title: "Success",
        description: "Poster fetched from TMDB successfully!",
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

      toast({
        title: "Error",
        description: `Failed to fetch poster: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleFetchThumbnailFromTMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first",
        variant: "destructive",
      });
      return;
    }

    if (!isValidTMDBId(formData.tmdbId)) {
      toast({
        title: "Error",
        description: "Please enter a valid TMDB ID (numbers only)",
        variant: "destructive",
      });
      return;
    }

    setIsFetching(true);

    try {
      toast({
        title: "Fetching Thumbnail",
        description: "Retrieving thumbnail from TMDB API...",
      });

      const result = await getComprehensiveContentData(formData.tmdbId);

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch thumbnail from TMDB");
      }

      if (!result.data.thumbnailUrl) {
        throw new Error("No thumbnail available for this content");
      }

      setFormData(prev => ({
        ...prev,
        thumbnailUrl: result.data.thumbnailUrl,
      }));

      toast({
        title: "Success",
        description: "Thumbnail fetched from TMDB successfully!",
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

      toast({
        title: "Error",
        description: `Failed to fetch thumbnail: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleFileUpload = (type: 'poster' | 'thumbnail' | 'subtitle') => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = type === 'subtitle' ? '.srt,.vtt' : 'image/*';
    
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        if (type === 'subtitle') {
          setFormData(prev => ({ ...prev, subtitleFile: file }));
          toast({
            title: "File uploaded",
            description: `Subtitle file "${file.name}" uploaded successfully`,
          });
        } else {
          // Simulate file upload and get URL
          const fakeUrl = `https://example.com/${type}/${file.name}`;
          handleInputChange(type === 'poster' ? 'posterUrl' : 'thumbnailUrl', fakeUrl);
          toast({
            title: "File uploaded",
            description: `${type} uploaded successfully`,
          });
        }
      }
    };
    
    input.click();
  };

  const addGenre = () => {
    if (newGenre.trim()) {
      setFormData(prev => ({ ...prev, genres: [...prev.genres, newGenre] }));
      setNewGenre("");
    }
  };

  const removeGenre = (index: number) => {
    setFormData(prev => ({ ...prev, genres: prev.genres.filter((_, i) => i !== index) }));
  };

  // Seasons and Episodes Management Functions
  const addSeason = () => {
    const newSeasonNumber = formData.seasons.length + 1;
    const newSeason: SeasonData = {
      seasonNumber: newSeasonNumber,
      title: `Season ${newSeasonNumber}`,
      description: "",
      posterUrl: "",
      episodes: []
    };

    setFormData(prev => ({
      ...prev,
      seasons: [...prev.seasons, newSeason],
      totalSeasons: prev.seasons.length + 1
    }));
  };

  const removeSeason = (seasonIndex: number) => {
    const updatedSeasons = formData.seasons.filter((_, index) => index !== seasonIndex);
    // Renumber seasons to maintain sequential order
    const renumberedSeasons = updatedSeasons.map((season, index) => ({
      ...season,
      seasonNumber: index + 1,
      title: season.title.includes('Season') ? `Season ${index + 1}` : season.title
    }));

    const totalEpisodes = renumberedSeasons.reduce((total, season) => total + season.episodes.length, 0);

    setFormData(prev => ({
      ...prev,
      seasons: renumberedSeasons,
      totalSeasons: renumberedSeasons.length,
      totalEpisodes
    }));
  };

  const updateSeason = (seasonIndex: number, field: keyof SeasonData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      seasons: prev.seasons.map((season, index) =>
        index === seasonIndex ? { ...season, [field]: value } : season
      )
    }));
  };

  const addEpisode = (seasonIndex: number) => {
    const season = formData.seasons[seasonIndex];
    const newEpisodeNumber = season.episodes.length + 1;
    const newEpisode: EpisodeData = {
      episodeNumber: newEpisodeNumber,
      title: `Episode ${newEpisodeNumber}`,
      description: "",
      videoLinks: "",
      secureVideoLinks: "",
      runtime: "",
      airDate: "",
      thumbnailUrl: ""
    };

    const updatedSeasons = formData.seasons.map((s, index) =>
      index === seasonIndex
        ? { ...s, episodes: [...s.episodes, newEpisode] }
        : s
    );

    const totalEpisodes = updatedSeasons.reduce((total, season) => total + season.episodes.length, 0);

    setFormData(prev => ({
      ...prev,
      seasons: updatedSeasons,
      totalEpisodes
    }));
  };

  const removeEpisode = (seasonIndex: number, episodeIndex: number) => {
    const updatedSeasons = formData.seasons.map((season, sIndex) => {
      if (sIndex === seasonIndex) {
        const updatedEpisodes = season.episodes.filter((_, eIndex) => eIndex !== episodeIndex);
        // Renumber episodes to maintain sequential order
        const renumberedEpisodes = updatedEpisodes.map((episode, index) => ({
          ...episode,
          episodeNumber: index + 1,
          title: episode.title.includes('Episode') ? `Episode ${index + 1}` : episode.title
        }));
        return { ...season, episodes: renumberedEpisodes };
      }
      return season;
    });

    const totalEpisodes = updatedSeasons.reduce((total, season) => total + season.episodes.length, 0);

    setFormData(prev => ({
      ...prev,
      seasons: updatedSeasons,
      totalEpisodes
    }));
  };

  const updateEpisode = (seasonIndex: number, episodeIndex: number, field: keyof EpisodeData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      seasons: prev.seasons.map((season, sIndex) =>
        sIndex === seasonIndex
          ? {
              ...season,
              episodes: season.episodes.map((episode, eIndex) =>
                eIndex === episodeIndex
                  ? {
                      ...episode,
                      [field]: value,
                      // Auto-encode video links for security
                      ...(field === 'videoLinks' && value.trim() ? { secureVideoLinks: encodeVideoLinks(value) } : {})
                    }
                  : episode
              )
            }
          : season
      )
    }));
  };

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Auto-save form data to prevent loss
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      if (formData.title || formData.description || formData.seasons.length > 0) {
        safeStorage.setItem('addTitleForm_autoSave', {
          ...formData,
          lastSaved: new Date().toISOString()
        });
      }
    }, 2000); // Auto-save every 2 seconds

    return () => clearTimeout(autoSaveTimer);
  }, [formData]);

  // Load auto-saved data on component mount
  useEffect(() => {
    const autoSavedData = safeStorage.getItem('addTitleForm_autoSave');
    if (autoSavedData && autoSavedData.title) {
      const shouldRestore = window.confirm(
        `Found auto-saved form data from ${new Date(autoSavedData.lastSaved).toLocaleString()}. Would you like to restore it?`
      );
      if (shouldRestore) {
        setFormData(prev => ({
          ...prev,
          ...autoSavedData,
          subtitleFile: null // Don't restore file objects
        }));
      }
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.type || !formData.category || formData.section_ids.length === 0) {
      toast({
        title: "Error",
        description: "Please fill in required fields (Title, Type, Category, and at least one Section)",
        variant: "destructive",
      });
      return;
    }

    // Additional validation for web series
    if (formData.type === "series") {
      if (formData.seasons.length === 0) {
        toast({
          title: "Error",
          description: "Web series must have at least one season",
          variant: "destructive",
        });
        return;
      }

      // Check if all seasons have at least one episode
      const seasonsWithoutEpisodes = formData.seasons.filter(season => season.episodes.length === 0);
      if (seasonsWithoutEpisodes.length > 0) {
        toast({
          title: "Error",
          description: `Season ${seasonsWithoutEpisodes[0].seasonNumber} must have at least one episode`,
          variant: "destructive",
        });
        return;
      }

      // Validate episode video links
      for (const season of formData.seasons) {
        for (const episode of season.episodes) {
          if (episode.videoLinks && !episode.secureVideoLinks) {
            episode.secureVideoLinks = encodeVideoLinks(episode.videoLinks);
          }
        }
      }
    }

    // Ensure video links are encoded for security
    const finalFormData = {
      ...formData,
      // Add section_ids for multiple section support
      section_ids: formData.section_ids
    };
    if (formData.videoLinks && !formData.secureVideoLinks) {
      const encoded = encodeVideoLinks(formData.videoLinks);
      finalFormData.secureVideoLinks = encoded;
      setFormData(prev => ({ ...prev, secureVideoLinks: encoded }));
    }

    // Validate video links if provided
    if (formData.videoLinks) {
      const links = parseVideoLinks(formData.videoLinks);
      const validLinks = links.filter(link => isValidVideoLink(link));
      if (links.length > 0 && validLinks.length === 0) {
        toast({
          title: "Warning",
          description: "No valid video links found. Please check the format.",
          variant: "destructive",
        });
        return;
      }
    }

    setIsSubmitting(true);

    try {
      // Save to database using production API
      const result = await apiService.createContent(finalFormData);

      if (result.success) {
        const contentId = result.data?.id;

        // If it's a web series, create seasons and episodes
        if (formData.type === "series" && formData.seasons.length > 0 && contentId) {
          try {
            console.log('Creating seasons and episodes for content:', contentId);
            console.log('Seasons data:', formData.seasons);

            for (let seasonIndex = 0; seasonIndex < formData.seasons.length; seasonIndex++) {
              const season = formData.seasons[seasonIndex];

              // Create season
              const seasonData = {
                seasonNumber: season.seasonNumber,
                title: season.title,
                description: season.description,
                posterUrl: season.posterUrl
              };

              console.log(`Creating season ${seasonIndex + 1}:`, seasonData);
              const seasonResult = await apiService.createSeason(contentId, seasonData);
              console.log(`Season ${seasonIndex + 1} result:`, seasonResult);

              if (seasonResult.success && season.episodes.length > 0) {
                const seasonId = seasonResult.data?.id;
                console.log(`Season ID: ${seasonId}, creating ${season.episodes.length} episodes`);

                // Create episodes for this season
                for (let episodeIndex = 0; episodeIndex < season.episodes.length; episodeIndex++) {
                  const episode = season.episodes[episodeIndex];
                  const episodeData = {
                    episodeNumber: episode.episodeNumber,
                    title: episode.title,
                    description: episode.description,
                    secure_video_links: episode.secureVideoLinks,
                    runtime: episode.runtime,
                    airDate: episode.airDate,
                    thumbnailUrl: episode.thumbnailUrl
                  };

                  console.log(`Creating episode ${episodeIndex + 1} for season ${seasonId}:`, episodeData);
                  const episodeResult = await apiService.createEpisode(contentId, seasonId, episodeData);
                  console.log(`Episode ${episodeIndex + 1} result:`, episodeResult);

                  if (!episodeResult.success) {
                    throw new Error(`Failed to create episode ${episode.episodeNumber}: ${episodeResult.error}`);
                  }
                }
              } else if (!seasonResult.success) {
                throw new Error(`Failed to create season ${season.seasonNumber}: ${seasonResult.error}`);
              }
            }

            // Clear auto-saved data on successful submission
            safeStorage.removeItem('addTitleForm_autoSave');

            toast({
              title: "Success",
              description: `Web series "${formData.title}" with ${formData.totalSeasons} seasons and ${formData.totalEpisodes} episodes saved successfully!`,
            });
          } catch (episodeError) {
            console.error('Error creating seasons/episodes:', episodeError);

            // Save current form data for recovery
            safeStorage.setItem('addTitleForm_errorRecovery', {
              ...formData,
              contentId,
              error: episodeError.message,
              timestamp: new Date().toISOString()
            });

            toast({
              title: "Partial Success",
              description: `Content saved but error creating seasons/episodes: ${episodeError.message}. Form data saved for recovery.`,
              variant: "destructive",
            });
          }
        } else {
          toast({
            title: "Success",
            description: `${formData.type} "${formData.title}" saved successfully to database!`,
          });
        }

        // Reset form after successful save
        setFormData({
          title: "",
          type: "movie",
          category: "",
          section: "",
          section_ids: [],
          tmdbId: "",
          year: "",
          genres: [],
          languages: [],
          description: "",
          posterUrl: "",
          thumbnailUrl: "",
          videoLinks: "",
          secureVideoLinks: "",
          quality: [],
          tags: "",
          imdbRating: "",
          runtime: "",
          studio: "",
          audioTracks: [],
          trailer: "",
          subtitleFile: null,
          subtitleUrl: "",
          isPublished: false,
          isFeatured: false,
          addToCarousel: false,
          // Web Series specific fields
          seasons: [],
          totalSeasons: 0,
          totalEpisodes: 0,
        });

        // Clear auto-saved data
        safeStorage.removeItem('addTitleForm_autoSave');

        // Clear TMDB data
        setTmdbData(null);
        setFetchError("");
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to save content to database",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to save content:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save content to database",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBulkAdd = () => {
    setShowBulkAddMode(true);
  };

  const handleBulkAddComplete = async (items: Partial<MediaItem>[]) => {
    try {
      // Convert MediaItem format to ContentFormData format
      const contentItems = items.map(item => ({
        title: item.title || "",
        type: item.type || "movie",
        category: item.category || "",
        section: formData.section || "", // Use current form section for bulk items
        section_ids: formData.section_ids.length > 0 ? formData.section_ids : [], // Use current form sections for bulk items
        tmdbId: "",
        year: item.year?.toString() || "",
        genres: item.genres || [],
        languages: item.languages || [],
        description: item.description || "",
        posterUrl: item.image || "",
        thumbnailUrl: item.coverImage || "",
        videoLinks: item.videoLinks || "",
        secureVideoLinks: "",
        quality: item.quality || [],
        tags: item.tags || "",
        imdbRating: item.imdbRating?.toString() || "",
        runtime: item.runtime?.toString() || "",
        studio: item.studio || "",
        audioTracks: item.audioTracks || [],
        trailer: item.trailer || "",
        subtitleFile: null,
        subtitleUrl: "",
        isPublished: item.isPublished ?? false,
        isFeatured: item.isFeatured ?? false,
        addToCarousel: item.addToCarousel ?? false,
      }));

      // Save to database using production API
      const result = await apiService.bulkCreateContent(contentItems);

      if (result.success) {
        toast({
          title: "Bulk import completed",
          description: `Successfully imported ${result.created} items to database. ${result.failed > 0 ? `${result.failed} items failed.` : ''}`,
        });
      } else {
        toast({
          title: "Bulk import failed",
          description: result.message || "Failed to import items to database",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Bulk import failed:', error);
      toast({
        title: "Bulk import failed",
        description: error instanceof Error ? error.message : "Failed to import items to database",
        variant: "destructive",
      });
    }
  };

  const handleCloseBulkAdd = () => {
    setShowBulkAddMode(false);
  };



  const fetchIMDbRating = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first to fetch rating",
        variant: "destructive",
      });
      return;
    }

    if (!isValidTMDBId(formData.tmdbId)) {
      toast({
        title: "Error",
        description: "Please enter a valid TMDB ID (numbers only)",
        variant: "destructive",
      });
      return;
    }

    setIsFetching(true);

    try {
      toast({
        title: "Fetching Rating",
        description: "Retrieving rating from TMDB API...",
      });

      const result = await getComprehensiveContentData(formData.tmdbId);

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch rating from TMDB");
      }

      if (!result.data.imdbRating) {
        throw new Error("No rating available for this content");
      }

      setFormData(prev => ({
        ...prev,
        imdbRating: result.data.imdbRating,
      }));

      toast({
        title: "Success",
        description: `Rating ${result.data.imdbRating}/10 fetched from TMDB!`,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

      toast({
        title: "Error",
        description: `Failed to fetch rating: ${errorMessage}`,
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleTMDBSearchSelect = async (tmdbId: string, contentType: 'movie' | 'tv') => {
    setFormData(prev => ({ ...prev, tmdbId }));

    // Automatically fetch data after selection
    try {
      const result = await getComprehensiveContentData(tmdbId, contentType);

      if (result.success) {
        const data = result.data;
        setTmdbData(result.rawData);

        setFormData(prev => ({
          ...prev,
          title: data.title,
          type: result.contentType,
          year: data.year,
          description: data.description,
          genres: data.genres,
          languages: data.languages,
          posterUrl: data.posterUrl,
          thumbnailUrl: data.thumbnailUrl,
          imdbRating: data.imdbRating,
          runtime: data.runtime,
          studio: data.studio,
          trailer: data.trailer,
        }));

        toast({
          title: "Content Loaded",
          description: `${result.contentType === 'movie' ? 'Movie' : 'TV Series'} data loaded from TMDB search!`,
        });
      }
    } catch (error) {
      console.error('Error loading selected content:', error);
    }
  };

  const handleOMDBSearchSelect = async (imdbId: string, contentType: 'movie' | 'tv', data?: any) => {
    if (data) {
      setFormData(prev => ({
        ...prev,
        tmdbId: data.id || imdbId, // Set the IMDb ID as tmdbId for reference
        title: data.title || prev.title,
        type: contentType,
        year: data.year || prev.year,
        description: data.plot || data.description || prev.description,
        genres: data.genres && data.genres.length > 0 ? data.genres : prev.genres,
        languages: data.languages && data.languages.length > 0 ? data.languages : prev.languages,
        imdbRating: data.rating || data.imdbRating || prev.imdbRating,
        runtime: data.runtime || prev.runtime,
        studio: data.studio || prev.studio,
        posterUrl: data.poster || data.posterUrl || prev.posterUrl,
        thumbnailUrl: data.thumbnailUrl || data.poster || data.posterUrl || prev.thumbnailUrl,
        tags: data.tags && data.tags.length > 0 ? data.tags.join(', ') : prev.tags,
        // Set web series specific fields if it's a TV series
        totalSeasons: contentType === 'tv' && data.totalSeasons ? parseInt(data.totalSeasons) : prev.totalSeasons,
      }));

      toast({
        title: "Content Loaded",
        description: `${contentType === 'movie' ? 'Movie' : 'TV Series'} data loaded from OMDb! All available fields populated.`,
      });
    }
  };

  const handleFetchFromOMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter an IMDb ID first",
        variant: "destructive",
      });
      return;
    }

    setIsFetching(true);

    try {
      toast({
        title: "Fetching from OMDb",
        description: "Retrieving content data from OMDb API...",
      });

      const { searchOMDbByImdbId } = await import("@/services/omdbService");
      const result = await searchOMDbByImdbId(formData.tmdbId);

      if (result.Response === "False") {
        throw new Error(result.Error || "Failed to fetch data from OMDb");
      }

      const { formatOMDbData, convertOMDbType } = await import("@/services/omdbService");
      const data = formatOMDbData(result);
      const contentType = convertOMDbType(result.Type);

      // Update form with fetched data
      setFormData(prev => ({
        ...prev,
        title: data.title,
        type: contentType,
        year: data.year,
        description: data.plot || data.description,
        genres: data.genres,
        languages: data.languages,
        imdbRating: data.rating || data.imdbRating,
        runtime: data.runtime,
        studio: data.studio,
        posterUrl: data.poster || data.posterUrl,
        thumbnailUrl: data.thumbnailUrl || data.poster || data.posterUrl,
        tags: data.tags && data.tags.length > 0 ? data.tags.join(', ') : prev.tags,
        // Set web series specific fields if it's a TV series
        totalSeasons: contentType === 'tv' && data.totalSeasons ? parseInt(data.totalSeasons) : prev.totalSeasons,
      }));

      toast({
        title: "Success",
        description: "Data fetched from OMDb successfully!",
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch from OMDb";
      setFetchError(errorMessage);

      toast({
        title: "OMDb Fetch Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Basic Details Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Basic Details</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title Name *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="Movie or Web Series name"
              required
              className="min-h-[44px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Type *</Label>
            <Select value={formData.type} onValueChange={(value: 'movie' | 'series' | 'requested') => setFormData({ ...formData, type: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="movie">Movie</SelectItem>
                <SelectItem value="series">Web Series</SelectItem>
                <SelectItem value="requested">Requested</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category *</Label>
            <Select value={formData.category} onValueChange={(value: string) => setFormData({ ...formData, category: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent className="max-h-60 overflow-y-auto">
                {CATEGORIES.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="section">Content Sections *</Label>
            <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border border-border rounded-md p-3">
              {sections.map((section) => (
                <label key={section.id} className="flex items-center space-x-2 p-2 bg-background/50 rounded cursor-pointer hover:bg-background/80 transition-colors">
                  <input
                    type="checkbox"
                    checked={formData.section_ids.includes(section.id)}
                    onChange={(e) => {
                      const sectionId = section.id;
                      if (e.target.checked) {
                        setFormData(prev => ({
                          ...prev,
                          section_ids: [...prev.section_ids, sectionId],
                          section: prev.section_ids.length === 0 ? sectionId.toString() : prev.section // Set first selection as primary for backward compatibility
                        }));
                      } else {
                        setFormData(prev => ({
                          ...prev,
                          section_ids: prev.section_ids.filter(id => id !== sectionId),
                          section: prev.section_ids.filter(id => id !== sectionId).length > 0 ? prev.section_ids.filter(id => id !== sectionId)[0].toString() : ""
                        }));
                      }
                    }}
                    className="rounded border-border text-primary focus:ring-primary"
                  />
                  <span className="text-sm text-foreground">{section.name}</span>
                </label>
              ))}
            </div>
            <p className="text-xs text-muted-foreground">
              Select one or more sections where this content will appear on the homepage
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tmdb-id">TMDB ID</Label>
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                id="tmdb-id"
                value={formData.tmdbId}
                onChange={(e) => setFormData({ ...formData, tmdbId: e.target.value })}
                placeholder="e.g. 123456"
                className="flex-1 min-h-[44px]"
              />
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowTMDBSearch(true)}
                  className="flex-1 sm:flex-none text-xs sm:text-sm px-3 sm:px-4 min-h-[44px]"
                >
                  <Search className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                  <span className="hidden sm:inline">TMDB</span>
                  <span className="sm:hidden">TMDB</span>
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleFetchFromTMDB}
                  disabled={!formData.tmdbId || isFetching}
                  className="flex-1 sm:flex-none text-xs sm:text-sm px-3 sm:px-4 min-h-[44px]"
                >
                  {isFetching ? (
                    <>
                      <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1 animate-spin" />
                      <span className="hidden sm:inline">Fetching...</span>
                      <span className="sm:hidden">Fetch...</span>
                    </>
                  ) : (
                    <>
                      <span className="hidden sm:inline">Fetch Data</span>
                      <span className="sm:hidden">Fetch</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">Search TMDB or enter ID manually to auto-fetch details</p>
            {fetchError && (
              <p className="text-xs text-red-500 mt-1">Error: {fetchError}</p>
            )}
            {tmdbData && (
              <div className="text-xs text-green-600 mt-1 flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Data fetched successfully from TMDB
              </div>
            )}
          </div>

          {/* OMDb Search Section - Positioned between TMDB and Genres */}
          <div className="space-y-2 border-t border-border/50 pt-4">
            <Label htmlFor="omdb-section">OMDb Search</Label>
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                id="omdb-imdb-id"
                value={formData.tmdbId}
                onChange={(e) => setFormData({ ...formData, tmdbId: e.target.value })}
                placeholder="Enter IMDb ID (e.g. tt1234567) or search by title"
                className="flex-1 min-h-[44px]"
              />
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowOMDBSearch(true)}
                  className="flex-1 sm:flex-none text-xs sm:text-sm px-3 sm:px-4 min-h-[44px] border-orange-500/30 text-orange-400 hover:bg-orange-500/10"
                >
                  <Search className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                  <span className="hidden sm:inline">Search OMDb</span>
                  <span className="sm:hidden">OMDb</span>
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleFetchFromOMDB}
                  disabled={!formData.tmdbId || isFetching}
                  className="flex-1 sm:flex-none text-xs sm:text-sm px-3 sm:px-4 min-h-[44px] bg-orange-600 hover:bg-orange-700 text-white"
                >
                  {isFetching ? (
                    <>
                      <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1 animate-spin" />
                      <span className="hidden sm:inline">Fetching...</span>
                      <span className="sm:hidden">Fetch...</span>
                    </>
                  ) : (
                    <>
                      <span className="hidden sm:inline">Fetch Data</span>
                      <span className="sm:hidden">Fetch</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">Search OMDb by title or enter IMDb ID to auto-fill details. Works as backup when TMDB data is unavailable.</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="year">Year of Release</Label>
            <Input
              id="year"
              value={formData.year}
              onChange={(e) => setFormData({ ...formData, year: e.target.value })}
              placeholder="YYYY"
              maxLength={4}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="genres">Genres</Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {formData.genres.map((genre, index) => (
              <span key={index} className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm flex items-center gap-1">
                {genre}
                <button
                  type="button"
                  onClick={() => removeGenre(index)}
                  className="ml-1 hover:text-destructive"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newGenre}
              onChange={(e) => setNewGenre(e.target.value)}
              placeholder="Add genre"
              onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addGenre())}
              className="flex-1"
            />
            <Button type="button" variant="outline" onClick={addGenre} className="shrink-0">
              Add
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Enter a description..."
            rows={4}
          />
        </div>

        {/* TMDB Data Preview */}
        {tmdbData && (
          <div className="mt-6 p-4 border border-green-200 rounded-lg bg-green-50 dark:bg-green-950 dark:border-green-800">
            <h4 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-2">
              TMDB Data Retrieved Successfully
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
              <div>
                <span className="font-medium">Title:</span> {tmdbData.title || tmdbData.name}
              </div>
              <div>
                <span className="font-medium">Type:</span> {'title' in tmdbData ? 'Movie' : 'TV Series'}
              </div>
              <div>
                <span className="font-medium">Rating:</span> {tmdbData.vote_average?.toFixed(1)}/10
              </div>
              <div>
                <span className="font-medium">Release:</span> {tmdbData.release_date || tmdbData.first_air_date}
              </div>
              {tmdbData.genres && (
                <div className="md:col-span-2">
                  <span className="font-medium">Genres:</span> {tmdbData.genres.map((g: any) => g.name).join(', ')}
                </div>
              )}
              {tmdbData.production_companies && tmdbData.production_companies.length > 0 && (
                <div className="md:col-span-2">
                  <span className="font-medium">Studio:</span> {tmdbData.production_companies[0].name}
                </div>
              )}
              {tmdbData.credits?.cast && tmdbData.credits.cast.length > 0 && (
                <div className="md:col-span-2">
                  <span className="font-medium">Cast:</span> {tmdbData.credits.cast.slice(0, 5).map((c: any) => c.name).join(', ')}
                </div>
              )}
            </div>
          </div>
        )}
      </section>

      {/* Seasons & Episodes Section - Only for Web Series */}
      {formData.type === "series" && (
        <section className="space-y-4 border-t border-border/50 pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-primary">Seasons & Episodes</h2>
              <p className="text-sm text-muted-foreground mt-1">
                Manage seasons and episodes for this web series
              </p>
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>Seasons: {formData.totalSeasons}</span>
              <span>Episodes: {formData.totalEpisodes}</span>
            </div>
          </div>

          <div className="space-y-6">
            {formData.seasons.length === 0 ? (
              <div className="text-center py-8 border-2 border-dashed border-border rounded-lg">
                <p className="text-muted-foreground mb-4">No seasons added yet</p>
                <Button type="button" onClick={addSeason} className="gap-2">
                  <Plus className="w-4 h-4" />
                  Add First Season
                </Button>
              </div>
            ) : (
              <>
                {formData.seasons.map((season, seasonIndex) => (
                  <div key={seasonIndex} className="border border-border rounded-lg p-4 space-y-4">
                    {/* Season Header */}
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Season {season.seasonNumber}</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {season.episodes.length} episodes
                        </span>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeSeason(seasonIndex)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Season Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor={`season-title-${seasonIndex}`}>Season Title</Label>
                        <Input
                          id={`season-title-${seasonIndex}`}
                          value={season.title}
                          onChange={(e) => updateSeason(seasonIndex, 'title', e.target.value)}
                          placeholder={`Season ${season.seasonNumber}`}
                        />
                      </div>
                      <div>
                        <Label htmlFor={`season-poster-${seasonIndex}`}>Season Poster URL</Label>
                        <Input
                          id={`season-poster-${seasonIndex}`}
                          value={season.posterUrl}
                          onChange={(e) => updateSeason(seasonIndex, 'posterUrl', e.target.value)}
                          placeholder="https://image-url.com/season-poster.jpg"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor={`season-description-${seasonIndex}`}>Season Description</Label>
                      <Textarea
                        id={`season-description-${seasonIndex}`}
                        value={season.description}
                        onChange={(e) => updateSeason(seasonIndex, 'description', e.target.value)}
                        placeholder="Enter season description..."
                        rows={2}
                      />
                    </div>

                    {/* Episodes Section */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">Episodes</h4>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => addEpisode(seasonIndex)}
                          className="gap-2"
                        >
                          <Plus className="w-4 h-4" />
                          Add Episode
                        </Button>
                      </div>

                      {season.episodes.length === 0 ? (
                        <div className="text-center py-4 border border-dashed border-border rounded">
                          <p className="text-sm text-muted-foreground">No episodes added yet</p>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {season.episodes.map((episode, episodeIndex) => (
                            <div key={episodeIndex} className="border border-border/50 rounded p-3 space-y-3">
                              <div className="flex items-center justify-between">
                                <h5 className="font-medium text-sm">Episode {episode.episodeNumber}</h5>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeEpisode(seasonIndex, episodeIndex)}
                                  className="text-destructive hover:text-destructive h-6 w-6 p-0"
                                >
                                  <X className="w-3 h-3" />
                                </Button>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`episode-title-${seasonIndex}-${episodeIndex}`} className="text-xs">
                                    Episode Title
                                  </Label>
                                  <Input
                                    id={`episode-title-${seasonIndex}-${episodeIndex}`}
                                    value={episode.title}
                                    onChange={(e) => updateEpisode(seasonIndex, episodeIndex, 'title', e.target.value)}
                                    placeholder={`Episode ${episode.episodeNumber}`}
                                    className="h-8 text-sm"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`episode-runtime-${seasonIndex}-${episodeIndex}`} className="text-xs">
                                    Runtime
                                  </Label>
                                  <Input
                                    id={`episode-runtime-${seasonIndex}-${episodeIndex}`}
                                    value={episode.runtime}
                                    onChange={(e) => updateEpisode(seasonIndex, episodeIndex, 'runtime', e.target.value)}
                                    placeholder="45 min"
                                    className="h-8 text-sm"
                                  />
                                </div>
                              </div>

                              <div>
                                <Label htmlFor={`episode-description-${seasonIndex}-${episodeIndex}`} className="text-xs">
                                  Episode Description
                                </Label>
                                <Textarea
                                  id={`episode-description-${seasonIndex}-${episodeIndex}`}
                                  value={episode.description}
                                  onChange={(e) => updateEpisode(seasonIndex, episodeIndex, 'description', e.target.value)}
                                  placeholder="Enter episode description..."
                                  rows={2}
                                  className="text-sm"
                                />
                              </div>

                              <div>
                                <Label htmlFor={`episode-video-${seasonIndex}-${episodeIndex}`} className="text-xs">
                                  Video Embed Links
                                </Label>
                                <Textarea
                                  id={`episode-video-${seasonIndex}-${episodeIndex}`}
                                  value={episode.videoLinks}
                                  onChange={(e) => updateEpisode(seasonIndex, episodeIndex, 'videoLinks', e.target.value)}
                                  placeholder="Paste embed iframe links (one per line)"
                                  rows={2}
                                  className="text-sm"
                                />
                                <p className="text-xs text-muted-foreground mt-1">
                                  Links will be automatically encoded for security
                                </p>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <Label htmlFor={`episode-air-date-${seasonIndex}-${episodeIndex}`} className="text-xs">
                                    Air Date
                                  </Label>
                                  <Input
                                    id={`episode-air-date-${seasonIndex}-${episodeIndex}`}
                                    type="date"
                                    value={episode.airDate}
                                    onChange={(e) => updateEpisode(seasonIndex, episodeIndex, 'airDate', e.target.value)}
                                    className="h-8 text-sm"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor={`episode-thumbnail-${seasonIndex}-${episodeIndex}`} className="text-xs">
                                    Thumbnail URL
                                  </Label>
                                  <Input
                                    id={`episode-thumbnail-${seasonIndex}-${episodeIndex}`}
                                    value={episode.thumbnailUrl}
                                    onChange={(e) => updateEpisode(seasonIndex, episodeIndex, 'thumbnailUrl', e.target.value)}
                                    placeholder="https://image-url.com/thumbnail.jpg"
                                    className="h-8 text-sm"
                                  />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                <div className="flex justify-center">
                  <Button type="button" onClick={addSeason} variant="outline" className="gap-2">
                    <Plus className="w-4 h-4" />
                    Add Another Season
                  </Button>
                </div>
              </>
            )}
          </div>
        </section>
      )}

      {/* Poster & Thumbnails Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Poster & Thumbnails</h2>
        
        <div className="space-y-4">
          <div>
            <Label>Upload Poster</Label>
            <div className="flex flex-col gap-2 mt-2">
              <div className="flex flex-col sm:flex-row gap-2">
                <Button type="button" variant="outline" className="flex items-center gap-2 min-h-[44px] justify-center">
                  <Upload className="w-4 h-4" />
                  Upload
                </Button>
                <span className="text-sm text-muted-foreground self-center text-center sm:text-left">or Paste URL</span>
              </div>
              <Input
                value={formData.posterUrl}
                onChange={(e) => setFormData({ ...formData, posterUrl: e.target.value })}
                placeholder="https://image-url.com/poster.jpg"
                className="w-full min-h-[44px]"
              />
            </div>
            <Button 
              type="button" 
              variant="link" 
              onClick={handleFetchPosterFromTMDB}
              disabled={!formData.tmdbId}
              className="mt-1 p-0 h-auto text-xs"
            >
              🎬 Fetch from TMDB
            </Button>
          </div>

          <div>
            <Label>Upload Thumbnail (optional)</Label>
            <div className="flex flex-col gap-2 mt-2">
              <div className="flex flex-col sm:flex-row gap-2">
                <Button type="button" variant="outline" className="flex items-center gap-2 min-h-[44px] justify-center">
                  <Upload className="w-4 h-4" />
                  Upload
                </Button>
                <span className="text-sm text-muted-foreground self-center text-center sm:text-left">or Paste URL</span>
              </div>
              <Input
                value={formData.thumbnailUrl}
                onChange={(e) => setFormData({ ...formData, thumbnailUrl: e.target.value })}
                placeholder="https://image-url.com/thumbnail.jpg"
                className="w-full min-h-[44px]"
              />
            </div>
            <div className="mt-1">
              <Button 
                type="button" 
                variant="link" 
                onClick={handleFetchThumbnailFromTMDB}
                disabled={!formData.tmdbId}
                className="p-0 h-auto text-xs break-all"
              >
                🎬 Fetch from TMDB
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Streaming Information Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Streaming Information</h2>
        
        <div className="space-y-4">
          <div>
            <Label>Add Video Embed Link(s)</Label>
            <Textarea
              placeholder="Paste embed iframe, e.g. //player.com/embed/..."
              className="mt-1 min-h-[80px]"
              value={formData.videoLinks}
              onChange={(e) => {
                const value = e.target.value;
                handleInputChange('videoLinks', value);
                // Auto-encode for security when links are added
                if (value.trim()) {
                  const encoded = encodeVideoLinks(value);
                  handleInputChange('secureVideoLinks', encoded);
                } else {
                  handleInputChange('secureVideoLinks', '');
                }
              }}
            />
            <div className="flex items-center justify-between mt-1">
              <span className="text-xs text-muted-foreground">Supports multiple links (add one per line)</span>
              {formData.videoLinks && (
                <div className="flex items-center gap-2">
                  {parseVideoLinks(formData.videoLinks).filter(link => isValidVideoLink(link)).length > 0 && (
                    <span className="text-xs text-green-600 flex items-center gap-1">
                      <CheckCircle className="w-3 h-3" />
                      {parseVideoLinks(formData.videoLinks).filter(link => isValidVideoLink(link)).length} valid link(s)
                    </span>
                  )}
                  {formData.secureVideoLinks && (
                    <span className="text-xs text-blue-600 flex items-center gap-1">
                      🔒 Secured
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
          <div>
            <Label>Preview Player</Label>
            {formData.secureVideoLinks ? (
              <SecureVideoPlayer
                encodedVideoLinks={formData.secureVideoLinks}
                title={formData.title || "Preview"}
                className="mt-2 max-w-2xl"
                showPlayerSelection={true}
              />
            ) : (
              <div className="rounded-lg bg-background border border-border shadow-inner mt-2 max-w-2xl">
                <div className="w-full aspect-video flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <Video className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Add video links above to preview player</p>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div>
            <Label>Video Quality</Label>
            <div className="flex gap-2 flex-wrap mt-2">
              {QUALITY.map(q => (
                <Toggle 
                  key={q}
                  variant="outline" 
                  size="sm"
                  pressed={formData.quality.includes(q)}
                  onPressedChange={() => handleQualityToggle(q)}
                >
                  {q}
                </Toggle>
              ))}
            </div>
            {formData.quality.length > 0 && (
              <div className="text-sm text-muted-foreground mt-2">
                Selected: {formData.quality.join(', ')}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Metadata & Tags Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Metadata & Tags</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <Label>Tags / Keywords</Label>
            <Input 
              placeholder="Separate with commas, e.g. action, hero, comedy" 
              className="mt-1"
              value={formData.tags}
              onChange={(e) => handleInputChange('tags', e.target.value)}
            />
          </div>
          <div>
            <Label>IMDb Rating</Label>
            <div className="flex items-center gap-2 mt-1">
              <Input 
                placeholder="e.g. 8.1" 
                className="w-32"
                value={formData.imdbRating}
                onChange={(e) => handleInputChange('imdbRating', e.target.value)}
              />
              <Button type="button" variant="outline" size="sm" onClick={fetchIMDbRating}>
                Fetch
              </Button>
            </div>
          </div>
          <div>
            <Label>Runtime (minutes)</Label>
            <Input 
              placeholder="e.g. 164" 
              className="mt-1"
              value={formData.runtime}
              onChange={(e) => handleInputChange('runtime', e.target.value)}
            />
          </div>
          <div>
            <Label>Studio / Production</Label>
            <Input 
              placeholder="Disney, Warner Bros, etc." 
              className="mt-1"
              value={formData.studio}
              onChange={(e) => handleInputChange('studio', e.target.value)}
            />
          </div>
        </div>
      </section>

      {/* Additional Features Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Additional Features</h2>
        
        <div className="space-y-4">

          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <Label>Upload Subtitle (SRT / VTT)</Label>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                className="flex items-center gap-2 mt-2"
                onClick={() => handleFileUpload('subtitle')}
              >
                <UploadCloud className="h-4 w-4" />
                Upload File
              </Button>
              {formData.subtitleFile && (
                <div className="text-sm text-muted-foreground mt-2">
                  Uploaded: {formData.subtitleFile.name}
                </div>
              )}
            </div>
            <div>
              <Label>Paste Subtitle Link (URL)</Label>
              <Input 
                className="mt-2" 
                placeholder="Paste URL..."
                value={formData.subtitleUrl}
                onChange={(e) => handleInputChange('subtitleUrl', e.target.value)}
              />
            </div>
          </div>
          
          <div>
            <Label>Audio Tracks (Language)</Label>
            <div className="mt-2 border border-border rounded-md p-2 bg-background max-h-[60px] overflow-y-auto">
              {LANGUAGES.map(language => (
                <label
                  key={language}
                  className="flex items-center gap-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground focus-within:bg-accent focus-within:text-accent-foreground p-1 rounded transition-colors duration-150"
                >
                  <input
                    type="checkbox"
                    checked={formData.audioTracks.includes(language)}
                    onChange={() => handleMultiSelect('audioTracks', language)}
                    className="rounded border-border bg-background text-primary focus:ring-primary focus:ring-2 focus:ring-offset-0"
                  />
                  <span className="select-none">{language}</span>
                </label>
              ))}
            </div>
          </div>
          
          <div>
            <Label>Add Trailer</Label>
            <Input 
              placeholder="YouTube link or paste TMDB trailer URL" 
              className="mt-2"
              value={formData.trailer}
              onChange={(e) => handleInputChange('trailer', e.target.value)}
            />
          </div>
        </div>
      </section>

      {/* Admin Controls Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Admin Controls</h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center justify-between p-4 border border-border rounded-lg bg-background/50 min-h-[80px]">
            <div className="flex-1 pr-4">
              <Label htmlFor="publish" className="text-base font-medium">Publish</Label>
              <p className="text-sm text-muted-foreground">Make content visible to users</p>
            </div>
            <Switch
              id="publish"
              checked={formData.isPublished}
              onCheckedChange={(checked) => handleInputChange('isPublished', checked)}
              className="flex-shrink-0"
            />
          </div>
          <div className="flex items-center justify-between p-4 border border-border rounded-lg bg-background/50 min-h-[80px]">
            <div className="flex-1 pr-4">
              <Label htmlFor="featured" className="text-base font-medium">Featured</Label>
              <p className="text-sm text-muted-foreground">Show in featured section</p>
            </div>
            <Switch
              id="featured"
              checked={formData.isFeatured}
              onCheckedChange={(checked) => handleInputChange('isFeatured', checked)}
              className="flex-shrink-0"
            />
          </div>
          <div className="flex items-center justify-between p-4 border border-border rounded-lg bg-background/50 min-h-[80px] sm:col-span-2 lg:col-span-1">
            <div className="flex-1 pr-4">
              <Label htmlFor="home-carousel" className="text-base font-medium">Home Carousel</Label>
              <p className="text-sm text-muted-foreground">Add to homepage slider</p>
            </div>
            <Switch
              id="home-carousel"
              checked={formData.addToCarousel}
              onCheckedChange={(checked) => handleInputChange('addToCarousel', checked)}
              className="flex-shrink-0"
            />
          </div>
        </div>
      </section>

      {/* Bulk Add Mode Section */}
      {showBulkAddMode && (
        <BulkAddMode
          onBulkAdd={handleBulkAddComplete}
          onClose={handleCloseBulkAdd}
        />
      )}

      <form onSubmit={handleSubmit}>
        <div className="flex flex-col sm:flex-row gap-4 pt-4 items-start sm:items-center">
          <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <Button
              type="submit"
              size="lg"
              disabled={isSubmitting}
              className="flex items-center gap-2 min-h-[48px] justify-center"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircle className="h-5 w-5" />
                  Save Title
                </>
              )}
            </Button>
            <Button type="button" variant="secondary" onClick={handleBulkAdd} className="min-h-[48px]">
              Bulk Add Mode
            </Button>
          </div>
          <span className="text-xs text-muted-foreground flex items-start gap-2 mt-2 sm:mt-0">
            <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
            <span>* Required fields. Other sections are optional and can be filled later.</span>
          </span>
        </div>
      </form>

      {/* TMDB Search Dialog */}
      <TMDBSearchDialog
        isOpen={showTMDBSearch}
        onClose={() => setShowTMDBSearch(false)}
        onSelectContent={handleTMDBSearchSelect}
      />

      {/* OMDb Search Dialog */}
      <OMDBSearchDialog
        isOpen={showOMDBSearch}
        onClose={() => setShowOMDBSearch(false)}
        onSelectContent={handleOMDBSearchSelect}
      />
    </div>
  );
}
