import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { MediaItem } from "@/types/media";
import { ContentFormData, LANGUAGES, QUALITY_OPTIONS, GENRE_OPTIONS, CONTENT_STATUSES, CATEGORIES } from "@/types/admin";
import { X, Upload, Link as LinkIcon, UploadCloud, CheckCircle, Video } from "lucide-react";
import SecureVideoPlayer from "@/components/SecureVideoPlayer";
import { encodeVideoLinks, parseVideoLinks, isValidVideoLink } from "@/utils/videoSecurity";
import apiService from "@/services/apiService";

interface ContentEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  content: MediaItem | null;
  onSave: (updatedContent: MediaItem) => void;
}

export default function ContentEditDialog({ isOpen, onClose, content, onSave }: ContentEditDialogProps) {
  const { toast } = useToast();
  const [showPosterUrl, setShowPosterUrl] = useState(false);
  const [showThumbnailUrl, setShowThumbnailUrl] = useState(false);
  const [sections, setSections] = useState<any[]>([]);

  const [formData, setFormData] = useState<ContentFormData>({
    title: "",
    type: "movie",
    category: "",
    section: "",
    section_ids: [],
    tmdbId: "",
    year: "",
    genres: [],
    languages: [],
    description: "",
    posterUrl: "",
    thumbnailUrl: "",
    videoLinks: "",
    secureVideoLinks: "",
    quality: [],
    tags: "",
    imdbRating: "",
    runtime: "",
    studio: "",
    audioTracks: [],
    trailer: "",
    subtitleFile: null,
    subtitleUrl: "",
    isPublished: false,
    isFeatured: false,
    addToCarousel: false,
    totalSeasons: 0,
    totalEpisodes: 0,
  });

  useEffect(() => {
    if (content) {
      setFormData({
        title: content.title || "",
        type: content.type || "movie",
        category: content.category || "",
        section: content.section || content.section_slug || "",
        section_ids: content.section_ids || [],
        tmdbId: content.tmdbId || "",
        year: content.year?.toString() || "",
        genres: content.genres || [],
        languages: content.languages || [],
        description: content.description || "",
        posterUrl: content.posterUrl || content.image || "",
        thumbnailUrl: content.thumbnailUrl || "",
        videoLinks: content.videoLinks || "",
        secureVideoLinks: content.secureVideoLinks || "",
        quality: content.quality || [],
        tags: content.tags || "",
        imdbRating: content.imdbRating || "",
        runtime: content.runtime || "",
        studio: content.studio || "",
        audioTracks: content.audioTracks || [],
        trailer: content.trailer || "",
        subtitleFile: null,
        subtitleUrl: content.subtitleUrl || "",
        isPublished: content.isPublished ?? true,
        isFeatured: content.isFeatured ?? false,
        addToCarousel: content.addToCarousel ?? false,
        totalSeasons: content.totalSeasons || 0,
        totalEpisodes: content.totalEpisodes || 0,
      });
      setShowPosterUrl(!!content.posterUrl || !!content.image);
      setShowThumbnailUrl(!!content.thumbnailUrl);
    }
  }, [content]);

  // Load sections
  useEffect(() => {
    const loadSections = async () => {
      try {
        const result = await apiService.getSections();
        if (result.success) {
          setSections(result.data);
        }
      } catch (error) {
        console.error('Failed to load sections:', error);
      }
    };

    loadSections();
  }, []);

  const handleInputChange = (field: keyof ContentFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleMultiSelect = (field: keyof ContentFormData, value: string) => {
    const currentArray = formData[field] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    handleInputChange(field, newArray);
  };

  const removeFromArray = (field: keyof ContentFormData, value: string) => {
    const currentArray = formData[field] as string[];
    handleInputChange(field, currentArray.filter(item => item !== value));
  };

  // Handle section selection
  const handleSectionToggle = (sectionId: number, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      section_ids: checked
        ? [...prev.section_ids, sectionId]
        : prev.section_ids.filter(id => id !== sectionId)
    }));
  };

  const handleSave = async () => {
    if (!content) return;

    try {
      // Prepare data for API call
      const updateData = {
        title: formData.title,
        type: formData.type,
        category: formData.category,
        section: formData.section,
        section_ids: formData.section_ids,
        tmdbId: formData.tmdbId,
        year: formData.year,
        genres: formData.genres,
        languages: formData.languages,
        description: formData.description,
        posterUrl: formData.posterUrl,
        thumbnailUrl: formData.thumbnailUrl,
        videoLinks: formData.videoLinks,
        secureVideoLinks: formData.secureVideoLinks,
        quality: formData.quality,
        tags: formData.tags,
        imdbRating: formData.imdbRating,
        runtime: formData.runtime,
        studio: formData.studio,
        audioTracks: formData.audioTracks,
        trailer: formData.trailer,
        subtitleUrl: formData.subtitleUrl,
        isPublished: formData.isPublished,
        isFeatured: formData.isFeatured,
        addToCarousel: formData.addToCarousel,
        totalSeasons: formData.totalSeasons,
        totalEpisodes: formData.totalEpisodes,
      };

      // Call API to update content
      const result = await apiService.updateContent(content.id, updateData);

      if (result.success) {
        // Create updated content object for local state
        const updatedContent: MediaItem = {
          ...content,
          ...updateData,
          year: parseInt(formData.year) || content.year,
          image: formData.posterUrl || content.image,
          coverImage: formData.posterUrl || content.coverImage,
          updatedAt: new Date().toISOString(),
        };

        onSave(updatedContent);
        toast({
          title: "Content updated",
          description: `"${formData.title}" has been updated successfully in database`,
        });
        onClose();
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to update content",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to update content:', error);
      toast({
        title: "Error",
        description: "Failed to update content in database",
        variant: "destructive",
      });
    }
  };

  const handleFileUpload = (type: 'poster' | 'thumbnail' | 'subtitle') => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = type === 'subtitle' ? '.srt,.vtt' : 'image/*';

    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        if (type === 'subtitle') {
          setFormData(prev => ({ ...prev, subtitleFile: file }));
          toast({
            title: "File uploaded",
            description: `Subtitle file "${file.name}" uploaded successfully`,
          });
        } else {
          // Simulate file upload and get URL
          const fakeUrl = `https://example.com/${type}/${file.name}`;
          handleInputChange(type === 'poster' ? 'posterUrl' : 'thumbnailUrl', fakeUrl);
          toast({
            title: "File uploaded",
            description: `${type} uploaded successfully`,
          });
        }
      }
    };

    input.click();
  };

  if (!content) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Content: {content.title}</DialogTitle>
          <DialogDescription>
            Edit the details of this {content.type}. All changes will be saved to the database.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Basic Information */}
          <section className="space-y-4">
            <h3 className="text-lg font-semibold text-primary">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Title *</Label>
                <Input
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter title"
                />
              </div>
              <div>
                <Label>Type</Label>
                <select
                  value={formData.type}
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  className="w-full bg-background border border-border rounded-md px-3 py-2 h-10"
                >
                  <option value="movie">Movie</option>
                  <option value="series">Web Series</option>
                </select>
              </div>
              <div>
                <Label>Category *</Label>
                <Select value={formData.category} onValueChange={(value: string) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60 overflow-y-auto">
                    {CATEGORIES.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Content Sections *</Label>
                <div className="grid grid-cols-2 gap-3 mt-2 max-h-48 overflow-y-auto border rounded-md p-3">
                  {sections.map((section) => (
                    <div key={section.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`section-${section.id}`}
                        checked={formData.section_ids.includes(section.id)}
                        onCheckedChange={(checked) => handleSectionToggle(section.id, checked)}
                      />
                      <Label
                        htmlFor={`section-${section.id}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {section.name}
                      </Label>
                    </div>
                  ))}
                </div>
                {formData.section_ids.length === 0 && (
                  <p className="text-sm text-red-500 mt-1">Please select at least one section</p>
                )}
              </div>
              <div>
                <Label>Year</Label>
                <Input
                  type="number"
                  value={formData.year}
                  onChange={(e) => handleInputChange('year', e.target.value)}
                  placeholder="2024"
                />
              </div>
              <div>
                <Label>TMDB ID</Label>
                <Input
                  value={formData.tmdbId}
                  onChange={(e) => handleInputChange('tmdbId', e.target.value)}
                  placeholder="123456"
                />
              </div>
            </div>
            
            <div>
              <Label>Description</Label>
              <Textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter description"
                rows={3}
              />
            </div>
          </section>

          {/* Media & Links */}
          <section className="space-y-4">
            <h3 className="text-lg font-semibold text-primary">Media & Links</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Poster Image</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleFileUpload('poster')}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowPosterUrl(!showPosterUrl)}
                  >
                    <LinkIcon className="h-4 w-4 mr-2" />
                    URL
                  </Button>
                </div>
                {showPosterUrl && (
                  <Input
                    className="mt-2"
                    value={formData.posterUrl}
                    onChange={(e) => handleInputChange('posterUrl', e.target.value)}
                    placeholder="https://example.com/poster.jpg"
                  />
                )}
              </div>
              
              <div>
                <Label>Thumbnail Image</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleFileUpload('thumbnail')}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowThumbnailUrl(!showThumbnailUrl)}
                  >
                    <LinkIcon className="h-4 w-4 mr-2" />
                    URL
                  </Button>
                </div>
                {showThumbnailUrl && (
                  <Input
                    className="mt-2"
                    value={formData.thumbnailUrl}
                    onChange={(e) => handleInputChange('thumbnailUrl', e.target.value)}
                    placeholder="https://example.com/thumbnail.jpg"
                  />
                )}
              </div>
            </div>

            <div>
              <Label>Video Links / Embed Code</Label>
              <Textarea
                value={formData.videoLinks}
                onChange={(e) => {
                  const value = e.target.value;
                  handleInputChange('videoLinks', value);
                  // Auto-encode for security when links are added
                  if (value.trim()) {
                    const encoded = encodeVideoLinks(value);
                    handleInputChange('secureVideoLinks', encoded);
                  } else {
                    handleInputChange('secureVideoLinks', '');
                  }
                }}
                placeholder="Enter streaming links or embed code (one per line)"
                rows={3}
              />
              <div className="flex items-center justify-between mt-1">
                <span className="text-xs text-muted-foreground">Supports multiple links (add one per line)</span>
                {formData.videoLinks && (
                  <div className="flex items-center gap-2">
                    {parseVideoLinks(formData.videoLinks).filter(link => isValidVideoLink(link)).length > 0 && (
                      <span className="text-xs text-green-600 flex items-center gap-1">
                        <CheckCircle className="w-3 h-3" />
                        {parseVideoLinks(formData.videoLinks).filter(link => isValidVideoLink(link)).length} valid link(s)
                      </span>
                    )}
                    {formData.secureVideoLinks && (
                      <span className="text-xs text-blue-600 flex items-center gap-1">
                        🔒 Secured
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div>
              <Label>Preview Player</Label>
              {formData.secureVideoLinks ? (
                <SecureVideoPlayer
                  encodedVideoLinks={formData.secureVideoLinks}
                  title={formData.title || "Preview"}
                  className="mt-2 max-w-2xl"
                  showPlayerSelection={true}
                />
              ) : (
                <div className="rounded-lg bg-background border border-border shadow-inner mt-2 max-w-2xl">
                  <div className="w-full aspect-video flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <Video className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Add video links above to preview player</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div>
              <Label>Trailer URL</Label>
              <Input
                value={formData.trailer}
                onChange={(e) => handleInputChange('trailer', e.target.value)}
                placeholder="https://youtube.com/watch?v=..."
              />
            </div>
          </section>

          {/* Genres & Categories */}
          <section className="space-y-4">
            <h3 className="text-lg font-semibold text-primary">Genres & Categories</h3>

            <div>
              <Label>Genres</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {GENRE_OPTIONS.map(genre => (
                  <Badge
                    key={genre}
                    variant={formData.genres.includes(genre) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => handleMultiSelect('genres', genre)}
                  >
                    {genre}
                  </Badge>
                ))}
              </div>
              {formData.genres.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {formData.genres.map(genre => (
                    <Badge key={genre} variant="secondary" className="text-xs">
                      {genre}
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={() => removeFromArray('genres', genre)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <div>
              <Label>Languages</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {LANGUAGES.map(language => (
                  <Badge
                    key={language}
                    variant={formData.languages.includes(language) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => handleMultiSelect('languages', language)}
                  >
                    {language}
                  </Badge>
                ))}
              </div>
              {formData.languages.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {formData.languages.map(language => (
                    <Badge key={language} variant="secondary" className="text-xs">
                      {language}
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={() => removeFromArray('languages', language)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <div>
              <Label>Quality</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {QUALITY_OPTIONS.map(quality => (
                  <Badge
                    key={quality}
                    variant={formData.quality.includes(quality) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => handleMultiSelect('quality', quality)}
                  >
                    {quality}
                  </Badge>
                ))}
              </div>
              {formData.quality.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {formData.quality.map(quality => (
                    <Badge key={quality} variant="secondary" className="text-xs">
                      {quality}
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={() => removeFromArray('quality', quality)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </section>

          {/* Additional Details */}
          <section className="space-y-4">
            <h3 className="text-lg font-semibold text-primary">Additional Details</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>IMDb Rating</Label>
                <Input
                  value={formData.imdbRating}
                  onChange={(e) => handleInputChange('imdbRating', e.target.value)}
                  placeholder="8.5"
                />
              </div>
              <div>
                <Label>Runtime (minutes)</Label>
                <Input
                  value={formData.runtime}
                  onChange={(e) => handleInputChange('runtime', e.target.value)}
                  placeholder="120"
                />
              </div>
              <div>
                <Label>Studio / Production</Label>
                <Input
                  value={formData.studio}
                  onChange={(e) => handleInputChange('studio', e.target.value)}
                  placeholder="Disney, Warner Bros, etc."
                />
              </div>
            </div>

            {/* Season/Episode counts for web series */}
            {formData.type === 'series' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Total Seasons</Label>
                  <Input
                    type="number"
                    min="0"
                    value={formData.totalSeasons}
                    onChange={(e) => handleInputChange('totalSeasons', parseInt(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label>Total Episodes</Label>
                  <Input
                    type="number"
                    min="0"
                    value={formData.totalEpisodes}
                    onChange={(e) => handleInputChange('totalEpisodes', parseInt(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>
              </div>
            )}

            <div>
              <Label>Tags (comma separated)</Label>
              <Input
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                placeholder="superhero, marvel, action"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <Label>Upload Subtitle (SRT / VTT)</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 mt-2"
                  onClick={() => handleFileUpload('subtitle')}
                >
                  <UploadCloud className="h-4 w-4" />
                  Upload File
                </Button>
                {formData.subtitleFile && (
                  <div className="text-sm text-muted-foreground mt-2">
                    Uploaded: {formData.subtitleFile.name}
                  </div>
                )}
              </div>
              <div>
                <Label>Paste Subtitle Link (URL)</Label>
                <Input
                  className="mt-2"
                  placeholder="Paste URL..."
                  value={formData.subtitleUrl}
                  onChange={(e) => handleInputChange('subtitleUrl', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label>Audio Tracks (Language)</Label>
              <div className="mt-2 border border-border rounded-md p-2 bg-background max-h-[60px] overflow-y-auto">
                {LANGUAGES.map(language => (
                  <label
                    key={language}
                    className="flex items-center gap-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground focus-within:bg-accent focus-within:text-accent-foreground p-1 rounded transition-colors duration-150"
                  >
                    <input
                      type="checkbox"
                      checked={formData.audioTracks.includes(language)}
                      onChange={() => handleMultiSelect('audioTracks', language)}
                      className="rounded border-border bg-background text-primary focus:ring-primary focus:ring-2 focus:ring-offset-0"
                    />
                    <span className="select-none">{language}</span>
                  </label>
                ))}
              </div>
              {formData.audioTracks.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {formData.audioTracks.map(track => (
                    <Badge key={track} variant="secondary" className="text-xs">
                      {track}
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={() => removeFromArray('audioTracks', track)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </section>

          {/* Publishing Options */}
          <section className="space-y-4">
            <h3 className="text-lg font-semibold text-primary">Publishing Options</h3>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="published"
                  checked={formData.isPublished}
                  onCheckedChange={(checked) => handleInputChange('isPublished', checked)}
                />
                <Label htmlFor="published">Published</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="featured"
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) => handleInputChange('isFeatured', checked)}
                />
                <Label htmlFor="featured">Featured Content</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="carousel"
                  checked={formData.addToCarousel}
                  onCheckedChange={(checked) => handleInputChange('addToCarousel', checked)}
                />
                <Label htmlFor="carousel">Add to Carousel</Label>
              </div>
            </div>
          </section>

          {/* Save/Cancel Buttons */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
