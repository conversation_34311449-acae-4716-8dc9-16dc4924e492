import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Play, Calendar, Clock, Loader2 } from "lucide-react";
import { MediaItem, Season, Episode } from "@/types/media";
import apiService from "@/services/apiService";

interface AllSeasonsModalProps {
  isOpen: boolean;
  onClose: () => void;
  content: MediaItem | null;
  onEpisodeSelect: (videoLinks: string, episodeTitle: string) => void;
}

export default function AllSeasonsModal({
  isOpen,
  onClose,
  content,
  onEpisodeSelect
}: AllSeasonsModalProps) {
  const [seasons, setSeasons] = useState<Season[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedSeason, setSelectedSeason] = useState<number | null>(null);

  // Load seasons and episodes from database
  const loadSeasons = async () => {
    if (!content || content.type !== 'series') return;

    try {
      setLoading(true);
      const result = await apiService.getContentSeasons(content.id);

      if (result.success && result.data) {
        const seasonsData = result.data.map(season => ({
          ...season,
          episodes: season.episodes || []
        }));
        
        setSeasons(seasonsData);

        // Auto-select first season only if no season is currently selected
        if (seasonsData.length > 0 && !selectedSeason) {
          setSelectedSeason(seasonsData[0].season_number || seasonsData[0].seasonNumber);
        }
      } else {
        console.error('Failed to load seasons:', result.message);
        setSeasons([]);
      }
    } catch (error) {
      console.error('Error loading seasons:', error);
      setSeasons([]);
    } finally {
      setLoading(false);
    }
  };

  // Load seasons when modal opens
  useEffect(() => {
    if (isOpen && content) {
      setSelectedSeason(null); // Reset selection when modal opens
      setSeasons([]); // Clear previous data
      loadSeasons();
    }
  }, [isOpen, content?.id]); // Only depend on isOpen and content.id, not the entire content object

  // Handle episode selection
  const handleEpisodeSelect = (episode: Episode) => {
    if (episode.secureVideoLinks) {
      onEpisodeSelect(
        episode.secureVideoLinks,
        `${episode.title} (S${episode.season_number || episode.seasonNumber || selectedSeason}E${episode.episode_number || episode.episodeNumber})`
      );
      onClose();

      // Scroll to video player after a short delay to allow modal to close
      setTimeout(() => {
        const videoPlayerElement = document.querySelector('.video-player-container');
        if (videoPlayerElement) {
          videoPlayerElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 300);
    }
  };

  if (!content || content.type !== 'series') {
    return null;
  }

  const totalSeasons = seasons.length;
  const totalEpisodes = seasons.reduce((total, season) => total + (season.episodes?.length || 0), 0);
  const currentSeason = seasons.find(s => (s.season_number || s.seasonNumber) === selectedSeason);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden bg-background/95 backdrop-blur-sm border-border/50">
        <DialogHeader className="pb-6 border-b border-border/50">
          <DialogTitle className="flex items-center gap-3 text-xl font-bold">
            <div className="p-2 rounded-lg bg-primary/10">
              <Play className="w-6 h-6 text-primary" />
            </div>
            <div>
              <div className="text-xl font-bold">{content.title}</div>
              <div className="text-sm font-normal text-muted-foreground">All Seasons & Episodes</div>
            </div>
          </DialogTitle>
          <div className="flex items-center gap-6 text-sm text-muted-foreground mt-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-primary"></div>
              <span>{totalSeasons} Season{totalSeasons !== 1 ? 's' : ''}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-secondary"></div>
              <span>{totalEpisodes} Episode{totalEpisodes !== 1 ? 's' : ''}</span>
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">Loading seasons...</span>
          </div>
        ) : seasons.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Play className="w-12 h-12 text-muted-foreground/50 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Seasons Available</h3>
            <p className="text-muted-foreground">This series doesn't have any seasons yet.</p>
          </div>
        ) : (
          <div className="flex gap-8 h-[65vh]">
            {/* Season Selection Sidebar */}
            <div className="w-72 flex-shrink-0 bg-muted/30 rounded-lg p-4">
              <h3 className="font-semibold mb-4 text-sm uppercase tracking-wide text-muted-foreground flex items-center gap-2">
                <div className="w-1 h-4 bg-primary rounded-full"></div>
                Seasons ({totalSeasons})
              </h3>
              <ScrollArea className="h-full">
                <div className="space-y-3">
                  {seasons.map((season) => {
                    const episodeCount = season.episodes?.length || 0;
                    const seasonNum = season.season_number || season.seasonNumber;
                    const isSelected = selectedSeason === seasonNum;

                    return (
                      <Button
                        key={season.id}
                        variant={isSelected ? "default" : "ghost"}
                        onClick={() => setSelectedSeason(seasonNum)}
                        className={`w-full justify-start p-4 h-auto rounded-lg transition-all duration-200 ${
                          isSelected
                            ? "bg-primary text-primary-foreground shadow-lg scale-[1.02]"
                            : "hover:bg-background/80 hover:shadow-md border border-transparent hover:border-border/50"
                        }`}
                      >
                        <div className="text-left w-full">
                          <div className="flex items-center gap-2 mb-1">
                            <div className={`w-2 h-2 rounded-full ${isSelected ? 'bg-primary-foreground' : 'bg-primary'}`}></div>
                            <div className="font-semibold">
                              Season {seasonNum}
                            </div>
                          </div>
                          {season.title && (
                            <div className="text-xs opacity-80 truncate mb-1">
                              {season.title}
                            </div>
                          )}
                          <div className="text-xs opacity-70 flex items-center gap-1">
                            <Play className="w-3 h-3" />
                            {episodeCount} episode{episodeCount !== 1 ? 's' : ''}
                          </div>
                        </div>
                      </Button>
                    );
                  })}
                </div>
              </ScrollArea>
            </div>

            {/* Episodes Content */}
            <div className="flex-1 min-w-0">
              {currentSeason ? (
                <div>
                  <div className="mb-6 p-4 bg-muted/20 rounded-lg border border-border/30">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-2 rounded-lg bg-primary/10">
                        <Play className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">
                          Season {currentSeason.season_number || currentSeason.seasonNumber}
                          {currentSeason.title && ` - ${currentSeason.title}`}
                        </h3>
                        <div className="text-sm text-muted-foreground flex items-center gap-2">
                          <span>{currentSeason.episodes?.length || 0} episodes</span>
                          {currentSeason.episodes?.length > 0 && (
                            <>
                              <span>•</span>
                              <span>Ready to watch</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    {currentSeason.description && (
                      <p className="text-sm text-muted-foreground mt-2 leading-relaxed">
                        {currentSeason.description}
                      </p>
                    )}
                  </div>

                  <ScrollArea className="h-[calc(100%-120px)]">
                    {currentSeason.episodes && currentSeason.episodes.length > 0 ? (
                      <div className="space-y-4">
                        {currentSeason.episodes.map((episode) => (
                          <Card key={episode.id} className="overflow-hidden hover:shadow-lg transition-all duration-200 hover:scale-[1.01] border-border/50 bg-background/50 backdrop-blur-sm">
                            <CardContent className="p-5">
                              <div className="flex items-start justify-between gap-4">
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-3 mb-3">
                                    <Badge variant="secondary" className="text-xs font-mono px-2 py-1 bg-primary/10 text-primary border-primary/20">
                                      E{episode.episode_number || episode.episodeNumber}
                                    </Badge>
                                    <h4 className="font-semibold text-base truncate">
                                      {episode.title}
                                    </h4>
                                  </div>
                                  
                                  {episode.description && (
                                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                                      {episode.description}
                                    </p>
                                  )}
                                  
                                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                    {episode.runtime && (
                                      <div className="flex items-center gap-1">
                                        <Clock className="w-3 h-3" />
                                        <span>{episode.runtime}</span>
                                      </div>
                                    )}
                                    {episode.airDate && (
                                      <div className="flex items-center gap-1">
                                        <Calendar className="w-3 h-3" />
                                        <span>{new Date(episode.airDate).toLocaleDateString()}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                
                                <Button
                                  size="sm"
                                  onClick={() => handleEpisodeSelect(episode)}
                                  disabled={!episode.secureVideoLinks}
                                  className="flex-shrink-0 bg-primary hover:bg-primary/90 text-primary-foreground shadow-md hover:shadow-lg transition-all duration-200"
                                >
                                  <Play className="w-4 h-4 mr-2 fill-current" />
                                  Watch Now
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-12 text-center">
                        <Play className="w-12 h-12 text-muted-foreground/50 mb-4" />
                        <h4 className="text-lg font-semibold mb-2">No Episodes</h4>
                        <p className="text-muted-foreground">
                          This season doesn't have any episodes yet.
                        </p>
                      </div>
                    )}
                  </ScrollArea>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-center">
                  <div>
                    <Play className="w-12 h-12 text-muted-foreground/50 mb-4 mx-auto" />
                    <h4 className="text-lg font-semibold mb-2">Select a Season</h4>
                    <p className="text-muted-foreground">
                      Choose a season from the sidebar to view its episodes.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
